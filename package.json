{"name": "lumi-tester", "version": "1.0.0", "description": "Testes automatizados para Lumi Ortho System", "main": "index.js", "scripts": {"test": "node playwright-test.js", "test:debug": "node playwright-test.js --debug", "test:load": "node playwright-test.js --load", "test:rps": "node playwright-test.js --rps", "test:load-light": "node playwright-test.js --load", "test:k6": "k6 run index.js", "install-browsers": "npx playwright install chromium"}, "keywords": ["testing", "automation", "playwright", "k6"], "author": "", "license": "ISC", "devDependencies": {"playwright": "^1.40.0"}}