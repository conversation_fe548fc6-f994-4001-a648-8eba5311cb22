const { chromium } = require('playwright');

// Configurações do teste
const CONFIG = {
  // Token de autenticação (mesmo do seu script K6)
  AUTH_TOKEN: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cG_hdOmSm_e6bmCQpeDPdrOq5CmQH4eUB_9DbQ9hl4o',
  
  // URLs da aplicação
  LOGIN_URL: 'https://app.lumiorthosystem.com.br/login',
  PATIENT_URL: 'https://app.lumiorthosystem.com.br/paciente/15',
  
  // Credenciais de login (se necessário)
  LOGIN_CREDENTIALS: {
    username: '<EMAIL>', // Substitua pelo seu usuário
    password: '12345678', // Substitua pela sua senha
  },

  // Configurações do browser
  BROWSER_OPTIONS: {
    headless: false, // Modo visual - você vai ver o browser
    slowMo: 500, // Atraso entre ações (em ms) para facilitar visualização
    devtools: false, // Abre as ferramentas de desenvolvedor
  },
  
  // Configurações de teste
  TEST_OPTIONS: {
    timeout: 30000, // Timeout geral (30s)
    waitTimeout: 5000, // Timeout para esperas específicas (5s)
    iterations: 2, // Quantas vezes executar o teste
    delayBetweenIterations: 2000, // Delay entre iterações (2s)
  }
};

async function performLogin(page) {
  console.log('🔐 Iniciando processo de login...');

  // Navega para a página de login
  await page.goto(CONFIG.LOGIN_URL, {
    waitUntil: 'networkidle',
    timeout: CONFIG.TEST_OPTIONS.timeout
  });

  console.log('📋 Título da página de login:', await page.title());

  // Aguarda a página carregar
  await page.waitForTimeout(100);

  // Procura pelos campos de login
  console.log('🔍 Procurando campos de login...');

  // Tenta diferentes seletores para o campo de usuário
  const userSelectors = [
    'input[name="username"]',
    'input[name="email"]',
    'input[name="user"]',
    'input[type="email"]',
    'input[placeholder*="usuário"]',
    'input[placeholder*="email"]',
    'input[placeholder*="Usuário"]',
    'input[placeholder*="Email"]',
    '#username',
    '#email',
    '#user'
  ];

  let userField = null;
  for (const selector of userSelectors) {
    try {
      const field = page.locator(selector).first();
      if (await field.count() > 0) {
        console.log(`✅ Campo de usuário encontrado: ${selector}`);
        userField = field;
        break;
      }
    } catch (error) {
      // Continua tentando
    }
  }

  // Tenta diferentes seletores para o campo de senha
  const passwordSelectors = [
    'input[name="password"]',
    'input[type="password"]',
    'input[placeholder*="senha"]',
    'input[placeholder*="Senha"]',
    '#password'
  ];

  let passwordField = null;
  for (const selector of passwordSelectors) {
    try {
      const field = page.locator(selector).first();
      if (await field.count() > 0) {
        console.log(`✅ Campo de senha encontrado: ${selector}`);
        passwordField = field;
        break;
      }
    } catch (error) {
      // Continua tentando
    }
  }

  if (!userField || !passwordField) {
    // Lista todos os inputs para debug
    console.log('🔍 Listando todos os campos input na página:');
    const allInputs = await page.locator('input').all();
    for (let i = 0; i < allInputs.length; i++) {
      const type = await allInputs[i].getAttribute('type') || 'text';
      const name = await allInputs[i].getAttribute('name') || 'sem nome';
      const placeholder = await allInputs[i].getAttribute('placeholder') || 'sem placeholder';
      console.log(`  Input ${i + 1}: type="${type}", name="${name}", placeholder="${placeholder}"`);
    }

    throw new Error('Campos de login não encontrados');
  }

  // Preenche os campos
  console.log('✏️ Preenchendo credenciais...');
  await userField.fill(CONFIG.LOGIN_CREDENTIALS.username);
  await passwordField.fill(CONFIG.LOGIN_CREDENTIALS.password);

  // Procura pelo botão de login
  const loginButtonSelectors = [
    'button[type="submit"]',
    'input[type="submit"]',
    'button:text("Entrar")',
    'button:text("Login")',
    'button:text("Acessar")',
    '.btn-login',
    '#login-button',
    '#submit'
  ];

  let loginButton = null;
  for (const selector of loginButtonSelectors) {
    try {
      const button = page.locator(selector).first();
      if (await button.count() > 0) {
        console.log(`✅ Botão de login encontrado: ${selector}`);
        loginButton = button;
        break;
      }
    } catch (error) {
      // Continua tentando
    }
  }

  if (!loginButton) {
    throw new Error('Botão de login não encontrado');
  }

  // Clica no botão de login
  console.log('👆 Clicando no botão de login...');
  await loginButton.click();

  // Aguarda o redirecionamento ou carregamento
  console.log('⏳ Aguardando login...');
  await page.waitForTimeout(100);

  // Verifica se o login foi bem-sucedido
  const currentUrl = page.url();
  console.log('📍 URL atual após login:', currentUrl);

  if (currentUrl.includes('/login')) {
    throw new Error('Login falhou - ainda na página de login');
  }

  console.log('✅ Login realizado com sucesso!');
}

async function runSingleTest(browser, iteration = 1) {
  console.log(`\n🚀 Iniciando teste - Iteração ${iteration}`);
  
  // Cria um novo contexto (como uma sessão isolada)
  const context = await browser.newContext({
    // Headers básicos
    extraHTTPHeaders: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  });

  // Cria uma nova página
  const page = await context.newPage();

  try {
    // Primeiro faz login
    await performLogin(page);

    console.log('📄 Navegando para a página do paciente...');

    // Navega para a página do paciente
    await page.goto(CONFIG.PATIENT_URL, {
      waitUntil: 'networkidle',
      timeout: CONFIG.TEST_OPTIONS.timeout
    });

    console.log('✅ Página do paciente carregada com sucesso');

    // Aguarda um pouco para você ver a página
    await page.waitForTimeout(100);

    console.log('🔍 Procurando pelo elemento "Planejamento"...');

    // Aguarda a página carregar completamente
    await page.waitForLoadState('networkidle');

    // Aguarda mais um pouco para elementos dinâmicos carregarem
    await page.waitForTimeout(100);

    // Debug: vamos ver o que tem na página
    console.log('📋 Título da página:', await page.title());

    // Vamos tentar diferentes seletores para "Planejamento"
    const possibleSelectors = [
      'li:has(span:text-is("Planejamento"))',
      'li:has(span:text("Planejamento"))',
    ];

    let foundElement = null;
    let workingSelector = null;

    for (const selector of possibleSelectors) {
      try {
        console.log(`🔍 Testando seletor: ${selector}`);
        const element = await page.locator(selector).first();
        const count = await element.count();

        if (count > 0) {
          console.log(`✅ Encontrado! Seletor: ${selector} (${count} elemento(s))`);
          foundElement = element;
          workingSelector = selector;
          break;
        } else {
          console.log(`❌ Não encontrado: ${selector}`);
        }
      } catch (error) {
        console.log(`❌ Erro com seletor ${selector}:`, error.message);
      }
    }

    if (!foundElement) {
      console.log('🔍 Vamos analisar a página em detalhes...');

      // Verifica se há elementos de navegação/menu
      const navElements = await page.locator('nav, .nav, .menu, .sidebar, [role="navigation"]').all();
      console.log(`📋 Encontrados ${navElements.length} elementos de navegação`);

      // Lista todos os elementos clicáveis
      const clickableElements = await page.locator('a, button, [role="button"], [onclick], .clickable').all();
      console.log(`🖱️ Encontrados ${clickableElements.length} elementos clicáveis`);

      for (let i = 0; i < Math.min(clickableElements.length, 15); i++) {
        const text = await clickableElements[i].textContent();
        const tagName = await clickableElements[i].evaluate(el => el.tagName);
        if (text?.trim()) {
          console.log(`  ${tagName} ${i + 1}: "${text.trim()}"`);
        }
      }

      console.log('🔍 Vamos listar todos os elementos <li> na página:');
      const allLis = await page.locator('li').all();
      for (let i = 0; i < Math.min(allLis.length, 10); i++) {
        const text = await allLis[i].textContent();
        console.log(`  Li ${i + 1}: "${text?.trim()}"`);
      }

      console.log('🔍 Procurando por texto que contenha "plan" (case insensitive):');
      const planElements = await page.locator('*').filter({ hasText: /plan/i }).all();
      for (let i = 0; i < Math.min(planElements.length, 10); i++) {
        const text = await planElements[i].textContent();
        const tagName = await planElements[i].evaluate(el => el.tagName);
        if (text?.trim()) {
          console.log(`  ${tagName}: "${text.trim()}"`);
        }
      }

      // Salva o HTML da página para análise
      const html = await page.content();
      const fs = require('fs');
      fs.writeFileSync(`page-debug-${Date.now()}.html`, html);
      console.log('💾 HTML da página salvo para análise');

      throw new Error('Elemento "Planejamento" não encontrado com nenhum seletor');
    }

    console.log(`👆 Clicando no elemento "Planejamento" usando: ${workingSelector}`);

    // Clica no elemento
    await foundElement.click();

    console.log('⏳ Aguardando imagens carregarem...');
    
    // Aguarda as imagens carregarem (com tratamento de erro)
    try {
      await page.waitForSelector('img.loaded', { 
        timeout: CONFIG.TEST_OPTIONS.waitTimeout 
      });
      console.log('🖼️ Imagens carregadas com sucesso');
    } catch (error) {
      console.log('⚠️ Timeout aguardando imagens, mas continuando...');
    }

    // Aguarda um pouco mais para você ver o resultado
    await page.waitForTimeout(100);

    console.log('✅ Teste concluído com sucesso!');

  } catch (error) {
    console.error('❌ Erro durante o teste:', error.message);
    
    // Tira um screenshot em caso de erro
    try {
      await page.screenshot({ 
        path: `error-screenshot-${iteration}-${Date.now()}.png`,
        fullPage: true 
      });
      console.log('📸 Screenshot do erro salvo');
    } catch (screenshotError) {
      console.error('Erro ao tirar screenshot:', screenshotError.message);
    }
  } finally {
    // Fecha o contexto (e a página)
    await context.close();
  }
}

async function runLoadTest() {
  console.log('🎯 Iniciando teste de carga com Playwright');
  console.log(`📊 Configurações: ${CONFIG.TEST_OPTIONS.iterations} iteração(s)`);
  
  // Inicia o browser
  const browser = await chromium.launch(CONFIG.BROWSER_OPTIONS);
  
  try {
    // Executa as iterações
    for (let i = 1; i <= CONFIG.TEST_OPTIONS.iterations; i++) {
      await runSingleTest(browser, i);
      
      // Aguarda entre iterações (exceto na última)
      if (i < CONFIG.TEST_OPTIONS.iterations) {
        console.log(`⏸️ Aguardando ${CONFIG.TEST_OPTIONS.delayBetweenIterations}ms antes da próxima iteração...`);
        await new Promise(resolve => setTimeout(resolve, CONFIG.TEST_OPTIONS.delayBetweenIterations));
      }
    }
    
    console.log('\n🎉 Todos os testes concluídos!');
    
  } finally {
    // Fecha o browser
    await browser.close();
  }
}

// Função para teste único (mais fácil para debug)
async function runSingleDebugTest() {
  console.log('🐛 Executando teste único para debug');
  
  const browser = await chromium.launch({
    ...CONFIG.BROWSER_OPTIONS,
    devtools: true, // Abre devtools para debug
  });
  
  await runSingleTest(browser, 1);
  
  // Não fecha o browser automaticamente no modo debug
  console.log('🔍 Browser mantido aberto para inspeção. Feche manualmente quando terminar.');
}

// Execução principal
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--debug')) {
    runSingleDebugTest().catch(console.error);
  } else {
    runLoadTest().catch(console.error);
  }
}

module.exports = {
  runLoadTest,
  runSingleDebugTest,
  CONFIG
};
