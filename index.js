import { browser } from 'k6/browser';
import { check, sleep } from 'k6';

// Configurações do teste (altere conforme necessário)
export const options = {
  scenarios: {
    ui_load_test: {
      executor: 'constant-arrival-rate', // Mantém uma taxa constante de requisições
      rate: 30, // 5 requisições por segundo (RPS) → Altere aqui!
      timeUnit: '1s', // Unidade de tempo para a taxa
      duration: '10s', // Duração total do teste
      preAllocatedVUs: 10, // Usuários virtuais pré-alocados
      maxVUs: 50, // Máximo de usuários virtuais (aumenta sob demanda)
      exec: 'browserSimulation', // Nome da função de execução
      options: {
        browser: {
          type: 'chromium', // Especifica o tipo de browser
          headless: false,
          slowMo: '500ms' // Opcional: reduz a velocidade para facilitar a observação
        },
      },
    },
  },
};

// Token de autenticação (substitua pelo seu)
const AUTH_TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cG_hdOmSm_e6bmCQpeDPdrOq5CmQH4eUB_9DbQ9hl4o'; // Ou cookie, se necessário

export async function browserSimulation() {
  // Inicializa uma nova página no navegador
  const page = await browser.newPage();

  try {
    // 1. Acessa a página com autenticação (token no header)
    await page.goto('https://app.lumiorthosystem.com.br/paciente/16', {
      headers: {
        Authorization: `Bearer ${AUTH_TOKEN}`,
      },
    });

    // 2. Clica no primeiro <li> que é pai de um <span> com o conteúdo "Planejamento"
    await page.locator('li:has(span:text-is("Planejamento"))').first().click();

    // // 3. Espera as imagens carregarem (opcional)
    // await page.waitForSelector('img.loaded', { timeout: 5000 }); // Timeout de 5s

  } catch (error) {
    console.error('Erro durante o teste:', error);
  }
  // Nota: O k6 browser gerencia automaticamente o fechamento das páginas
}