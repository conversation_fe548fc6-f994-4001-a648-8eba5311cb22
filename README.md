# Lumi Tester

Projeto de testes automatizados para o Lumi Ortho System usando K6 e Playwright.

## 📁 Arquivos

- `index.js` - Script original com K6 (headless)
- `playwright-test.js` - Nova versão com Playwright (modo visual)
- `package.json` - Dependências do projeto

## 🚀 Instalação

1. Instale as dependências:
```bash
npm install
```

2. Instale o browser Chromium para o Playwright:
```bash
npm run install-browsers
```

## 🎯 Como usar

### Playwright (Recomendado)

**Teste sequencial (modo visual):**
```bash
npm run test
```

**Teste em modo debug (mantém browser aberto):**
```bash
npm run test:debug
```

**Teste de carga/RPS (30 abas simultâneas, headless):**
```bash
npm run test:load
# ou
npm run test:rps
```

### K6 (Original - Headless)

```bash
npm run test:k6
```

## 🔧 Configurações

### Playwright (`playwright-test.js`)

Você pode ajustar as configurações no objeto `CONFIG`:

- `AUTH_TOKEN` - Token de autenticação
- `APP_URL` - URL da aplicação
- `BROWSER_OPTIONS.headless` - `false` para modo visual, `true` para headless
- `BROWSER_OPTIONS.slowMo` - Velocidade das ações (em ms)
- `TEST_OPTIONS.iterations` - Quantas vezes executar o teste
- `TEST_OPTIONS.delayBetweenIterations` - Delay entre execuções

### Exemplo de configuração para teste sequencial:

```javascript
TEST_OPTIONS: {
  timeout: 30000,
  waitTimeout: 5000,
  iterations: 5, // Executa 5 vezes
  delayBetweenIterations: 1000, // 1s entre execuções
}
```

### Configurações para teste de carga (RPS):

```javascript
LOAD_TEST_OPTIONS: {
  concurrentTabs: 30, // Número de abas simultâneas
  duration: 60, // Duração do teste em segundos
  rampUpTime: 10, // Tempo para abrir todas as abas
  headless: true, // Modo headless para performance
  timeout: 15000, // Timeout menor para teste de carga
  waitTimeout: 3000, // Timeout menor para esperas
}
```

## 🐛 Debug

O modo debug (`npm run test:debug`) é útil para:
- Ver exatamente o que está acontecendo
- Inspecionar elementos na página
- Verificar se os seletores estão corretos
- Analisar erros em tempo real

## 📸 Screenshots

Em caso de erro, o Playwright automaticamente salva screenshots com o nome:
`error-screenshot-{iteração}-{timestamp}.png`

## 🆚 Diferenças entre K6 e Playwright

| Aspecto | K6 | Playwright |
|---------|----|-----------| 
| **Visualização** | Limitada (headless funciona melhor) | Excelente (modo visual funciona perfeitamente) |
| **Performance** | Otimizado para carga | Melhor para funcional |
| **Debug** | Mais difícil | Muito fácil |
| **Relatórios** | Métricas de performance | Screenshots e logs detalhados |
| **Uso recomendado** | Testes de carga/stress | Testes funcionais e debug |

## 💡 Dicas

1. **Para visualizar a interação**: Use o Playwright (`npm run test`)
2. **Para testes de carga**: Use o K6 (`npm run test:k6`)
3. **Para debug**: Use `npm run test:debug`
4. **Ajuste o `slowMo`** no Playwright para ver as ações mais devagar
5. **Use `iterations`** para simular múltiplos usuários com Playwright
