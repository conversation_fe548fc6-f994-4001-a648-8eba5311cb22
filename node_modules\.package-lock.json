{"name": "lumi-tester", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"node_modules/playwright": {"version": "1.53.0", "resolved": "https://registry.npmjs.org/playwright/-/playwright-1.53.0.tgz", "integrity": "sha512-ghGNnIEYZC4E+YtclRn4/p6oYbdPiASELBIYkBXfaTVKreQUYbMUYQDwS12a8F0/HtIjr/CkGjtwABeFPGcS4Q==", "dev": true, "license": "Apache-2.0", "dependencies": {"playwright-core": "1.53.0"}, "bin": {"playwright": "cli.js"}, "engines": {"node": ">=18"}, "optionalDependencies": {"fsevents": "2.3.2"}}, "node_modules/playwright-core": {"version": "1.53.0", "resolved": "https://registry.npmjs.org/playwright-core/-/playwright-core-1.53.0.tgz", "integrity": "sha512-mGLg8m0pm4+mmtB7M89Xw/GSqoNC+twivl8ITteqvAndachozYe2ZA7srU6uleV1vEdAHYqjq+SV8SNxRRFYBw==", "dev": true, "license": "Apache-2.0", "bin": {"playwright-core": "cli.js"}, "engines": {"node": ">=18"}}}}